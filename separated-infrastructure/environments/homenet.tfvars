# Environment-specific variables for Homenet
# These reference the same infrastructure as the original shared-variables files

# Infrastructure Configuration
region              = "us-east-1"
region_abbreviated  = "ue1"
environment         = "homenet"
build_number        = "homenet-latest"
launched_by         = "github-actions"
launched_on         = ""  # Will be set dynamically in workflow

# Account Configuration (from shared-variables.nonprod.tf)
account_type = "nonprod"
account_id   = "************"

# EFS Configuration (from shared-variables.nonprod.tf and nonprod.ue1.tf)
efs_id         = "fs-5fcfafaa"
security_group = "sg-0358b7ef2c51fcbd1"

# EC2 Configuration (from shared-variables.nonprod.tf)
asg_ami              = "ami-0fe5f366c083f59ca"
asg_min_size         = "1"
asg_max_size         = "2"
asg_desired_capacity = "1"
instance_type        = "m5.large"

# Network Configuration (from shared-variables.global.tf)
homenet_cidr = "204.11.136.0/32"
ais_cidr     = "107.1.95.66/32"
remote_cidr  = "108.252.235.16/32"
nfs_cidr     = "100.71.40.0/22"  # from shared-variables.nonprod.ue1.tf

# Application Configuration (from shared-variables.global.tf)
application             = "ais10"
application_abbreviated = "a10"
service                 = "web"
slack_contact           = "+ais-operations"
component               = "processing-apps-cluster"
processing_apps_component_id = "CI0934608"

# Autoscaling Configuration (homenet - minimal scaling)
asg_scheduling_enabled          = "false"
asg_extended_scheduling_enabled = "false"
asg_scheduling_normal_map = {}
asg_scheduling_extended_map = {}

# S3 Configuration (from shared-variables.nonprod.tf)
ini_bucket = "ais.1-0.application.packages.np.ue1"

# State Management
cluster_state_bucket = "ais.nonprod.ue1.infrastructure.tf.state"
cluster_state_key    = "AIS-1.0/homenet/processing-apps-cluster"
sns_state_bucket     = "ais.nonprod.ue1.infrastructure.tf.state"
sns_state_key        = "AIS-1.0/nonprod/sns-alerts"
