#==============================================================
# Module Outputs (accessible to consumers)
#==============================================================

output "cluster_name" {
  description = "Name of the ECS cluster"
  value       = aws_ecs_cluster.default.name
}

output "cluster_arn" {
  description = "ARN of the ECS cluster"
  value       = aws_ecs_cluster.default.arn
}

output "cluster_id" {
  description = "ID of the ECS cluster"
  value       = aws_ecs_cluster.default.id
}

output "vpc_id" {
  description = "VPC ID where the cluster is deployed"
  value       = data.aws_vpc.vpc.id
}

output "private_subnet_ids" {
  description = "List of private subnet IDs where the cluster is deployed"
  value       = data.aws_subnet_ids.private.ids
}

output "security_group_id" {
  description = "Security group ID created for the ECS cluster EC2 instances"
  value       = aws_security_group.default.id
}

output "autoscaling_group_name" {
  description = "Name of the Auto Scaling Group"
  value       = aws_autoscaling_group.default.name
}

output "autoscaling_group_arn" {
  description = "ARN of the Auto Scaling Group"
  value       = aws_autoscaling_group.default.arn
}

output "launch_configuration_name" {
  description = "Name of the Launch Configuration"
  value       = aws_launch_configuration.default.name
}

output "environment" {
  description = "Environment name for referencing in task deployments"
  value       = var.environment
}

output "region" {
  description = "AWS Region where the cluster is deployed"
  value       = var.region
}

output "account_type" {
  description = "Account type (prod/nonprod) for referencing in task deployments"
  value       = var.account_type
}
