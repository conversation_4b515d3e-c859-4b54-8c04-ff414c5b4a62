# ECS Cluster Infrastructure Module

This module creates and manages the core ECS cluster infrastructure that can be shared across multiple applications and task definitions. It includes the ECS cluster, Auto Scaling Group, Launch Configuration, Security Groups, and supporting infrastructure.

## Overview

This is a **one-time deployment** module that sets up the foundational ECS cluster infrastructure. Once deployed, multiple applications can deploy their task definitions to reference this cluster.

## Resources Created

- **ECS Cluster**: The main container orchestration cluster
- **Auto Scaling Group**: Manages EC2 instances for the cluster
- **Launch Configuration**: Defines EC2 instance configuration
- **Security Group**: Controls network access to cluster instances
- **VPC Data Sources**: References existing VPC and subnet infrastructure

## Key Features

- **Shared Infrastructure**: One cluster can support multiple applications
- **Auto Scaling**: Configurable scaling parameters for cluster capacity
- **Security**: Properly configured security groups with minimal required access
- **EFS Integration**: Automatic mounting of shared file systems
- **CloudWatch Integration**: Built-in monitoring and logging configuration
- **SSM Integration**: Systems Manager for patch management and monitoring

## Usage

### Basic Deployment

```hcl
module "ecs_cluster" {
  source = "./separated-infrastructure/cluster-infrastructure"

  # Required Variables
  region              = "us-east-1"
  region_abbreviated  = "ue1"
  environment         = "production"
  build_number        = "**************"
  launched_by         = "ci-cd-system"
  launched_on         = "2025-06-24T12:00:00Z"
  
  # EFS Configuration
  efs_id             = "fs-5fcfafaa"
  security_group     = "sg-0358b7ef2c51fcbd1"
  
  # EC2 Configuration
  asg_ami            = "ami-0fe5f366c083f59ca"
  asg_min_size       = 3
  asg_max_size       = 18
  asg_desired_capacity = 3
  instance_type      = "m5.large"
  
  # Account Configuration
  account_type       = "nonprod"
}
```

### Production Configuration

```hcl
module "ecs_cluster" {
  source = "./separated-infrastructure/cluster-infrastructure"

  # Production-specific settings
  environment          = "production"
  asg_min_size        = 18
  asg_max_size        = 18
  asg_desired_capacity = 18
  instance_type       = "m5.4xlarge"
  account_type        = "prod"
  
  # ... other required variables
}
```

## Variable Reference

### Required Variables

| Variable | Type | Description |
|----------|------|-------------|
| `region` | string | AWS Region for deployment |
| `region_abbreviated` | string | Abbreviated region name (e.g., ue1) |
| `environment` | string | Environment name (production, staging, etc.) |
| `build_number` | string | Build identifier for tracking |
| `launched_by` | string | User or system that launched the deployment |
| `launched_on` | string | Timestamp of deployment |
| `efs_id` | string | EFS file system ID for shared storage |
| `security_group` | string | Security group ID for EFS access |
| `asg_ami` | string | AMI ID for ECS-optimized instances |
| `account_type` | string | Account type (prod or nonprod) |

### Optional Variables

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `application` | string | "ais10" | Application name for tagging |
| `service` | string | "web" | Service name for tagging |
| `component` | string | "processing-apps-cluster" | Component name |
| `asg_min_size` | string | "3" | Minimum instances in ASG |
| `asg_max_size` | string | "18" | Maximum instances in ASG |
| `asg_desired_capacity` | string | "3" | Desired instances in ASG |
| `instance_type` | string | "m5.large" | EC2 instance type |
| `ec2_key_name` | string | "AIS10" | EC2 key pair name |

## Outputs

| Output | Description |
|--------|-------------|
| `cluster_name` | Name of the ECS cluster |
| `cluster_arn` | ARN of the ECS cluster |
| `cluster_id` | ID of the ECS cluster |
| `vpc_id` | VPC ID where cluster is deployed |
| `private_subnet_ids` | List of private subnet IDs |
| `security_group_id` | Security group ID for cluster instances |
| `autoscaling_group_name` | Name of the Auto Scaling Group |
| `environment` | Environment name for reference |
| `region` | AWS Region |
| `account_type` | Account type for reference |

## Integration with Task Deployment Module

The task deployment module references this cluster infrastructure using Terraform remote state:

```hcl
data "terraform_remote_state" "ecs_cluster" {
  backend = "s3"
  config = {
    bucket = "ais.nonprod.ue1.infrastructure.tf.state"
    key    = "AIS-1.0/production/processing-apps-cluster"
    region = "us-east-1"
  }
}

# Reference cluster ARN in task definitions
cluster_arn = data.terraform_remote_state.ecs_cluster.outputs.cluster_arn
```

## Deployment Process

1. **Plan the deployment**:
   ```bash
   terraform plan -var-file="production.tfvars"
   ```

2. **Apply the infrastructure**:
   ```bash
   terraform apply -var-file="production.tfvars"
   ```

3. **Verify cluster creation**:
   ```bash
   aws ecs describe-clusters --clusters <cluster-name>
   ```

## State Management

The cluster infrastructure uses S3 backend for state management. Ensure your Terraform configuration includes:

```hcl
terraform {
  backend "s3" {
    bucket = "ais.nonprod.ue1.infrastructure.tf.state"
    key    = "AIS-1.0/production/processing-apps-cluster"
    region = "us-east-1"
  }
}
```

## Prerequisites

- AWS VPC with private subnets must exist
- EFS file system must be created and accessible
- Appropriate IAM roles must exist:
  - `ais10-ec2-for-ec2ecs-role` for EC2 instances
- EC2 Key Pair must exist for SSH access

## Security Considerations

- EC2 instances are deployed in private subnets only
- Security groups allow SSH access only from specified CIDR blocks
- EFS access is controlled through separate security groups
- All resources are properly tagged for compliance and cost tracking

## Maintenance

- **AMI Updates**: Update the `asg_ami` variable with latest ECS-optimized AMI
- **Scaling**: Adjust ASG parameters based on workload requirements
- **Instance Types**: Update `instance_type` for performance or cost optimization
- **Security Groups**: Review and update CIDR blocks as network requirements change

## Troubleshooting

### Common Issues

1. **ASG instances not joining cluster**: Check ECS agent configuration in userdata
2. **EFS mount failures**: Verify EFS security group and network connectivity
3. **SSH access denied**: Confirm security group rules and key pair configuration

### Monitoring

- CloudWatch metrics are automatically configured for ASG
- ECS cluster metrics available in CloudWatch
- SSM agent installed for systems management

## Cost Optimization

- Use appropriate instance types for workload
- Configure ASG scheduling for non-production environments
- Monitor and adjust capacity based on actual usage patterns
