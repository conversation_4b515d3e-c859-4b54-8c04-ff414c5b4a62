###############################################################
# Pass-through Variables
###############################################################

variable "application" {
  description = "Name of the application to be used when tagging aws resources"
  default     = "ais10"
}

variable "application_abbreviated" {
  description = "Abbreviation for Application name"
  default     = "a10"
}

variable "service" {
  description = "The service that this configuration builds"
  default     = "web"
}

variable "region" {
  description = "AWS Region"
}

variable "region_abbreviated" {
  description = "Abbreviated region name (e.g., ue1 for us-east-1)"
}

variable "environment" {
  description = "Name of the environment we are building"
}

variable "launched_by" {
  description = "Name of the user or tool that launched this environment"
}

variable "launched_on" {
  description = "Timestamp representing when the environment was last built"
}

variable "slack_contact" {
  description = "Slack channel that should be notified by the various aws monkeys"
  default     = "+ais-operations"
}

variable "build_number" {
  description = "Build Number"
}

variable "homenet_cidr" {
  description = "The public CIDR block of the HomeNet network."
  default     = "************/32"
}

variable "ais_cidr" {
  description = "The public CIDR block of the AIS network."
  default     = "***********/32"
}

variable "remote_cidr" {
  description = "The public CIDR block of the Remote networks."
  default     = "**************/32"
}

variable "component" {
  description = "Name of the component being deployed"
  default     = "processing-apps-cluster"
}

variable "component_id" {
  description = "The Component ID of the processing apps"
  default     = "CI0934608"
}

variable "processing_apps_component_id" {
  description = "The Component ID of the processing apps"
  default     = "CI0934608"
}

variable "nfs_cidr" {
  description = "CIDR block for NFS access"
  default     = ""
}

variable "efs_id" {
  description = "EFS file system ID for shared storage"
}

###############################################################
# AutoScale Variables
###############################################################

variable "asg_min_size" {
  description = "Default min number of EC2s in autoscale group"
  default     = "3"
}

variable "asg_max_size" {
  description = "Default max number of EC2s in autoscale group"
  default     = "18"
}

variable "asg_desired_capacity" {
  description = "Default desired number of EC2s in autoscale group"
  default     = "3"
}

variable "asp_scale_out_adjustment" {
  description = "Instance side to use for 1.0 webstack"
  default     = "4"
}

variable "asp_scale_out_cooldown" {
  description = "Instance side to use for 1.0 webstack"
  default     = "60"
}

variable "asp_scale_in_adjustment" {
  description = "Instance side to use for 1.0 webstack"
  default     = "-1"
}

variable "asp_scale_in_cooldown" {
  description = "Instance side to use for 1.0 webstack"
  default     = "60"
}

variable "security_group" {
  description = "The security group the ec2 instances should use."
}

variable "asg_scheduling_enabled" {
  description = "Whether or not the autoscaling schedulers are enabled for normal business hours"
  default     = "false"
}

variable "asg_scheduling_normal_map" {
  description = "Autoscaling scheduler map for normal business hour scale-up/down"
  type        = map(string)
  default = {
    "_default" = ""
  }
}

variable "asg_extended_scheduling_enabled" {
  description = "Whether or not the autoscaling schedulers are enabled for extended (change-day) business hours"
  default     = "false"
}

variable "asg_scheduling_extended_map" {
  description = "Autoscaling scheduler map for extended business hour scale-up/down on first/second of the month"
  type        = map(string)
  default = {
    "_default" = ""
  }
}

###############################################################
# EC2 Variables
###############################################################

variable "asg_ami" {
  description = "AMI ID for the 1.0 webstack AMI pre-configured with the LAMP stack, code, and ini deployment"
}

variable "instance_type" {
  description = "Instance side to use for 1.0 webstack"
  default     = "m5.large"
}

variable "ec2_key_name" {
  description = "Name of the key pair to associate with EC2 instances"
  default     = "AIS10"
}

###############################################################
# ECS Cluster Variables
###############################################################

variable "ecs_logging" {
  default     = "[\"json-file\",\"awslogs\"]"
  description = "Adding logging option to ECS that the Docker containers can use. It is possible to add fluentd as well"
}

###############################################################
# CloudWatch Variables
###############################################################

variable "cw_alarm_low_cpu_threshold" {
  description = "The threshold value to use on the Low CPU CloudWatch alarm."
  default     = "20"
}

variable "cw_alarm_low_cpu_period" {
  description = "The metric period value to use on the Low CPU CloudWatch alarm."
  default     = "300"
}

variable "cw_alarm_low_cpu_evaluation_periods" {
  description = "The evaluation periods value to use on the Low CPU CloudWatch alarm."
  default     = "40"
}

variable "cw_alarm_high_cpu_threshold" {
  description = "The threshold value to use on the High CPU CloudWatch alarm."
  default     = "40"
}

variable "cw_alarm_high_cpu_failsafe_threshold" {
  description = "The threshold value to use on the High CPU CloudWatch alarm."
  default     = "55"
}

variable "cw_alarm_high_cpu_period" {
  description = "The metric period value to use on the High CPU CloudWatch alarm."
  default     = "60"
}

variable "cw_alarm_high_cpu_evaluation_periods" {
  description = "The evaluation periods value to use on the High CPU CloudWatch alarm."
  default     = "2"
}

variable "cw_alarm_high_cpu_datapoint" {
  description = "number of data points on after which alarm should be triggerd"
  default     = "2"
}

variable "cw_alarm_high_cpu_failsafe_evaluation_periods" {
  description = "The evaluation periods value to use on the High CPU CloudWatch alarm."
  default     = "2"
}

variable "cw_alarm_high_cpu_failsafe_datapoint" {
  description = "number of data points on after which alarm should be triggerd"
  default     = "2"
}

variable "cw_alarm_low_mem_threshold" {
  description = "The threshold value to use on the Low Memory CloudWatch alarm."
  default     = "30"
}

variable "cw_alarm_low_mem_period" {
  description = "The metric period value to use on the Low Memory CloudWatch alarm."
  default     = "300"
}

variable "cw_alarm_low_mem_evaluation_periods" {
  description = "The evaluation periods value to use on the Low Memory CloudWatch alarm."
  default     = "40"
}

variable "cw_alarm_high_mem_threshold" {
  description = "The threshold value to use on the High Memory CloudWatch alarm."
  default     = "50"
}

variable "cw_alarm_high_mem_failsafe_threshold" {
  description = "The threshold value to use on the High Memory CloudWatch alarm."
  default     = "60"
}

variable "cw_alarm_high_mem_period" {
  description = "The metric period value to use on the High Memory CloudWatch alarm."
  default     = "60"
}

variable "cw_alarm_high_mem_evaluation_periods" {
  description = "The evaluation periods value to use on the High Memory CloudWatch alarm."
  default     = "2"
}

variable "cw_alarm_high_mem_failsafe_evaluation_periods" {
  description = "The evaluation periods value to use on the High Memory CloudWatch alarm."
  default     = "2"
}

variable "account_type" {
  description = "Type of AWS account (prod or nonprod)"
}
