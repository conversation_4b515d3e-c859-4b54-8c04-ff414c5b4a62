terraform {
  required_providers {
    aws = {
      version = "3.75.2"
      source = "hashicorp/aws"
    }
  }
  backend "s3" {}
}

provider "aws" {
  region = var.region

  default_tags {
    tags = {
      "coxauto:ci-id" = var.processing_apps_component_id
    }
  }
  ignore_tags {
    key_prefixes = ["cai:catalog"]
  }
}

# Data source to reference the ECS cluster created by cluster infrastructure
data "terraform_remote_state" "ecs_cluster" {
  backend = "s3"

  config = {
    bucket = var.cluster_state_bucket
    key    = var.cluster_state_key
    region = var.region
  }
}

# Data source for SNS alerts (optional - can be null)
data "terraform_remote_state" "sns_alerts" {
  count   = var.sns_state_key != "" ? 1 : 0
  backend = "s3"

  config = {
    bucket = var.sns_state_bucket
    key    = var.sns_state_key
    region = var.region
  }
}

# ECS Task Definition Module
module "task" {
  source = "./modules/task"

  environment               = var.environment
  region                    = var.region
  country_iso_code          = var.country_iso_code
  task_friendly_name        = var.task_friendly_name
  container_definition_path = var.container_definition_path
  task_role_arn             = var.task_role_arn
  execution_role_arn        = var.execution_role_arn
  requires_compatibilites   = var.requires_compatibilites
  image_url_name_tag        = var.image_url_name_tag != "" ? var.image_url_name_tag : "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/describevehicleextract_dotnet_${lower(var.environment)}:${var.image_tag}"
  ini_bucket                = var.ini_bucket
  rds_backup_bucket         = var.rds_backup_bucket
  rrri_topic_arn            = var.rrri_topic_arn
}

# CloudWatch Log Group Module
module "log_group" {
  source = "./modules/log-group"

  application   = var.application
  service       = var.service
  region        = var.region
  environment   = var.environment
  build_number  = var.build_number
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact
  component     = var.component

  name              = var.task_friendly_name
  log_group_name    = "${var.task_friendly_name}_${var.environment}"
  retention_in_days = var.retention_in_days
}

# Scheduled Event Module (CloudWatch Events)
module "scheduled_event" {
  source = "./modules/scheduled-event"

  environment         = var.environment
  task_friendly_name  = var.task_friendly_name
  schedule_expression = var.schedule_expression
  role_arn            = var.event_rule_arn
  task_arn            = module.task.task_arn
  cluster_arn         = data.terraform_remote_state.ecs_cluster.outputs.cluster_arn
  task_count          = var.task_count
  enabled             = var.enabled
}

# CloudWatch Alarm Module
module "alarm" {
  source = "./modules/alarm"

  application   = var.application
  service       = var.service
  region        = var.region
  environment   = var.environment
  build_number  = var.build_number
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact
  component     = var.component

  name                  = var.task_friendly_name
  log_group_name        = module.log_group.log_group_name
  alarm_action_arn      = var.alarm_action_arn != "" ? var.alarm_action_arn : try(data.terraform_remote_state.sns_alerts[0].outputs.warning_alert_arn, "")
  alarm_description     = var.alarm_description
  metric_filter_pattern = var.alarm_metric_filter_pattern
}
