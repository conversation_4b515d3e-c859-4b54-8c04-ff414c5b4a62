#==============================================================
# Module Outputs (accessible to consumers)
#==============================================================

output "task_arn" {
  description = "ARN of the ECS task definition"
  value       = module.task.task_arn
}

output "task_name" {
  description = "Name of the ECS task definition"
  value       = module.task.task_name
}

output "log_group_name" {
  description = "Name of the CloudWatch log group"
  value       = module.log_group.log_group_name
}

# Note: log_group_arn, event_rule_name, and event_rule_arn outputs 
# are not provided by the current module implementations

output "cluster_arn_referenced" {
  description = "ARN of the ECS cluster being used (from remote state)"
  value       = data.terraform_remote_state.ecs_cluster.outputs.cluster_arn
}

output "environment" {
  description = "Environment name"
  value       = var.environment
}

output "task_friendly_name" {
  description = "Friendly name of the task"
  value       = var.task_friendly_name
}
