resource "aws_cloudwatch_log_group" "default" {
  name              = var.log_group_name
  retention_in_days = var.retention_in_days

  tags = {
    Application  = var.application
    Environment  = var.environment
    Service      = var.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Component    = var.component
  }
}
