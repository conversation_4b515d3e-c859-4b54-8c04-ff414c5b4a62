###############################################################
# Pass-through Variables
###############################################################
variable "application" {
  description = "Name of the application to be used when tagging aws resources"
  default     = "ais10"
}

variable "service" {
  description = "The service that this configuration builds"
  default     = "web"
}

variable "region" {
  description = "AWS Region"
}

variable "environment" {
  description = "Name of the environment we are building"
}

variable "launched_by" {
  description = "Name of the user or tool that launched this environment"
}

variable "launched_on" {
  description = "Timestamp representing when the environment was last built"
}

variable "slack_contact" {
  description = "Slack channel that should be notified by the various aws monkeys"
  default     = "+ais-operations"
}

variable "build_number" {
  description = "Build Number"
}

variable "component" {
  description = "Name of the component being deployed"
  default     = "task-definitions"
}

variable "country_iso_code" {
  description = "Country ISO code for the task (e.g., US)"
  default     = "US"
}

variable "processing_apps_component_id" {
  description = "The Component ID of the processing apps"
  default     = "CI0934608"
}

###############################################################
# Cluster Reference Variables
###############################################################

variable "cluster_state_bucket" {
  description = "S3 bucket name where the ECS cluster terraform state is stored"
}

variable "cluster_state_key" {
  description = "S3 key path where the ECS cluster terraform state is stored"
}

variable "sns_state_bucket" {
  description = "S3 bucket name where the SNS alerts terraform state is stored"
  default     = ""
}

variable "sns_state_key" {
  description = "S3 key path where the SNS alerts terraform state is stored"
  default     = ""
}

###############################################################
# Task Definition Variables
###############################################################

variable "task_friendly_name" {
  description = "The friendly name of the task definition"
}

variable "container_definition_path" {
  description = "Path to the container definition json file"
}

variable "task_role_arn" {
  description = "The ARN of IAM role that allows your Amazon ECS container task to make calls to other AWS services."
}

variable "execution_role_arn" {
  description = "The Amazon Resource Name (ARN) of the task execution role that the Amazon ECS container agent and the Docker daemon can assume."
}

variable "requires_compatibilites" {
  description = "A set of launch types required by the task. The valid values are EC2 and FARGATE."
  default     = "EC2"
}

variable "image_url_name_tag" {
  description = "The ECR URL and image name:tag that should be used for the container."
  default     = ""
}

variable "account_id" {
  description = "AWS Account ID"
}

variable "ini_bucket" {
  description = "The S3 bucket to grab the INI files from."
}

variable "rds_backup_bucket" {
  description = "The S3 bucket that nightly rds backups store the dump to."
  default     = ""
}

variable "rrri_topic_arn" {
  description = "RRR&I SNS Topic ARN"
  default     = ""
}

variable "task_count" {
  description = "The number of tasks that should run"
  default     = 1
}

###############################################################
# Log Group Variables
###############################################################

variable "retention_in_days" {
  description = "Specifies the number of days you want to retain log events in the specified log group."
}

###############################################################
# Cloudwatch Event Rule Variables
###############################################################

variable "schedule_expression" {
  description = "The cron expression the scheduled task should use"
}

variable "event_rule_arn" {
  description = "The ARN of the IAM role for CloudWatch Events to assume"
}

variable "enabled" {
  description = "Should the scheduled cloudwatch event be enabled"
  default     = false
}

###############################################################
# CloudWatch Alarm Variables
###############################################################

variable "alarm_action_arn" {
  description = "The ARN of the action to execute when this alarm transitions into an ALARM state"
  default     = ""
}

variable "alarm_description" {
  description = "The description for the alarm"
  default     = "Task execution alarm"
}

variable "alarm_metric_filter_pattern" {
  description = "A valid CloudWatch Logs filter pattern for extracting metric data out of ingested log events."
  default     = "ERROR"
}

variable "image_tag" {
  description = "Docker image tag to deploy"
  default     = "latest"
}
