# Use the official .NET 8 SDK image for build
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Set ARGs and ENVs for private repositories
ARG ARTIFACTORY_USERNAME
ENV ARTIFACTORY_USERNAME=${ARTIFACTORY_USERNAME}

ARG ARTIFACTORY_API_KEY
ENV ARTIFACTORY_API_KEY=${ARTIFACTORY_API_KEY}

# Copy the project and restore dependencies
COPY ./src/VehicleExportWorkerService/VehicleExportWorkerService.csproj ./
COPY ./src/VehicleExportWorkerService/NuGet.Config ./
RUN dotnet restore --configfile ./NuGet.Config


# Copy everything else and build
COPY VehicleExportWorkerService/. ./VehicleExportWorkerService/
WORKDIR ./src/VehicleExportWorkerService ./
RUN dotnet build -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish -c Release -o /app/publish

# Use ASP.NET Core Runtime for the final image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app

# Set environment (default to Production)
ENV DOTNET_ENVIRONMENT=Production

# Expose any ports if needed (uncomment if required)
EXPOSE 80

ENTRYPOINT ["dotnet", "VehicleExportWorkerService.dll"]