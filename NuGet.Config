﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <packageSources>
    <clear />
    <add key="vAuto" value="https://artifactory.coxautoinc.com/artifactory/api/nuget/v3/vauto-nuget" protocolVersion="3" />
    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
  </packageSources>

  <config>
    <add key="defaultPushSource" value="https://artifactory.coxautoinc.com/artifactory/api/nuget/v3/vauto-nuget-local" protocolVersion="3" />
  </config>

  <packageSourceCredentials>
    <vAuto>
      <add key="Username" value="%ArtifactoryUser%" />
      <add key="ClearTextPassword" value="%ArtifactoryPassword%" />
    </vAuto>
  </packageSourceCredentials>
</configuration>