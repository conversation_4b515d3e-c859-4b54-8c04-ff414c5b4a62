name: .NET Build Check

on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, synchronize, reopened]
    branches: [ main ]
  
env:
  ArtifactoryUser: ${{ secrets.ARTIFACTORY_USERNAME }}
  ArtifactoryPassword: ${{ secrets.ARTIFACTORY_API_KEY }}
  DotnetVersion: 8.0.x

jobs:
  build:
    runs-on: [ self-hosted, CAI-Enterprise-Ubuntu-Latest ]
    steps:
    - uses: actions/checkout@v4
    - name: Setup .NET
      uses: actions/setup-dotnet@v1
      with:
        dotnet-version: ${{ env.DotnetVersion }}
    - name: Restore dependencies
      run: dotnet restore
    - name: Build
      run: dotnet build --no-restore
    - name: Test
      run: dotnet test --no-build