terraform {
  required_providers {
    aws = {
      version = "3.76.0"
      source = "hashicorp/aws"
    }
  }
  backend "s3" {}
}

provider "aws" {
  region = var.region

  default_tags {
    tags = {
      "coxauto:ci-id" = var.processing_apps_component_id
    }
  }
  ignore_tags {
    key_prefixes = ["cai:catalog"]
  }
}

module "ecs-cluster" {
  source = "../../../modules/processing-apps/ecs-cluster"

  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = var.service
  region                  = var.region
  region_abbreviated      = var.regions_abbreviated[var.region]
  environment             = var.environment
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact
  component               = var.component

  homenet_cidr = var.homenet_cidr
  ais_cidr     = var.ais_cidr
  remote_cidr  = var.remote_cidr
  nfs_cidr     = var.nfs_cidr

  vpc_id             = data.aws_vpc.vpc.id
  private_subnet_ids = data.aws_subnet_ids.private.ids
  availability_zones = var.availability_zones
  efs_id             = var.efs_shares[var.region]
  security_group     = var.efs_security_group[var.region]
  asg_ami            = var.ecs_ami_ids[var.region]
  instance_type      = "m5.4xlarge"

  asg_scheduling_enabled                  = "true"
  asg_scheduling_normal_map               = var.autoscale_scheduling_normal_map
  asg_extended_scheduling_enabled         = "false"
  account_type                            = var.account_type
  component_id                            = var.processing_apps_component_id
}

